import { useState, useEffect } from "react"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { invoiceFormSchema, type InvoiceFormData } from "@/lib/validations"
import { createInvoice, updateInvoice } from "@/lib/api/invoices-client"
import { getCurrentUserIdOrFallback } from "@/lib/auth"
import { Invoice } from "@/lib/types"
import { type Currency } from "@/lib/utils/currency"

interface UseInvoiceFormProps {
  invoice?: Invoice
  preselectedClientId?: string
  preselectedProjectId?: string
  onSuccess: () => void
  onOpenChange: (open: boolean) => void
  open: boolean
}

export function useInvoiceForm({
  invoice,
  preselectedClientId,
  preselectedProjectId,
  onSuccess,
  onOpenChange,
  open,
}: UseInvoiceFormProps) {
  const [loading, setLoading] = useState(false)
  const isEditing = !!invoice

  const form = useForm<InvoiceFormData>({
    resolver: zodResolver(invoiceFormSchema),
    defaultValues: {
      client_id: "",
      project_id: "none",
      amount: 0,
      tax_amount: 0,
      discount_amount: 0,
      discount_type: "amount" as const,
      total_amount: 0,
      currency: "IDR" as Currency,
      status: "draft",
      due_date: "",
      paid_date: "",
      items: [{ id: "1", description: "", quantity: 1, rate: 0, amount: 0 }],
      notes: "",
      milestone_type: "standard" as const,
      parent_invoice_id: undefined,
      milestone_percentage: undefined,
      sequence_number: 1,
    },
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  })

  // Reset form when invoice data changes
  useEffect(() => {
    if (open) {
      // Handle invoice items - they might be stored as JSON in the database
      let defaultItems = [{ id: "1", description: "", quantity: 1, rate: 0, amount: 0 }]

      if (invoice?.items) {
        if (Array.isArray(invoice.items)) {
          defaultItems = invoice.items as { id: string; description: string; quantity: number; rate: number; amount: number; }[]
        } else if (typeof invoice.items === 'string') {
          try {
            defaultItems = JSON.parse(invoice.items)
          } catch (e) {
            console.error('Failed to parse invoice items:', e)
          }
        }
      }
      
      form.reset({
        client_id: invoice?.client_id || preselectedClientId || "",
        project_id: invoice?.project_id || preselectedProjectId || "none",
        amount: invoice?.amount || 0,
        tax_amount: invoice?.tax_amount || 0,
        discount_amount: invoice?.discount_amount || 0,
        discount_type: (invoice?.discount_type as "amount" | "percentage") || "amount",
        total_amount: invoice?.total_amount || 0,
        currency: (invoice?.currency as Currency) || "IDR",
        status: invoice?.status || "draft",
        due_date: invoice?.due_date || "",
        paid_date: invoice?.paid_date || "",
        items: defaultItems,
        notes: invoice?.notes || "",
        milestone_type: invoice?.milestone_type || "standard",
        parent_invoice_id: invoice?.parent_invoice_id,
        milestone_percentage: invoice?.milestone_percentage,
        sequence_number: invoice?.sequence_number || 1,
      })
    }
  }, [invoice, preselectedClientId, preselectedProjectId, open, form])

  const onSubmit = async (data: InvoiceFormData) => {
    setLoading(true)
    try {
      // Get current user ID or use fallback for development
      const currentUserId = await getCurrentUserIdOrFallback()

      const invoiceData = {
        ...data,
        created_by: currentUserId,
        // Convert empty date strings to null for PostgreSQL compatibility
        due_date: data.due_date && data.due_date.trim() !== "" ? data.due_date : null,
        paid_date: data.paid_date && data.paid_date.trim() !== "" ? data.paid_date : null,
        // Handle optional project_id (convert "none" to null)
        project_id: data.project_id && data.project_id.trim() !== "" && data.project_id !== "none" ? data.project_id : null,
      }

      if (isEditing) {
        await updateInvoice(invoice.id, invoiceData)
      } else {
        await createInvoice(invoiceData)
      }
      onSuccess()
      onOpenChange(false)
      form.reset()
    } catch (error) {
      console.error("Failed to save invoice:", error)
      alert("Failed to save invoice. Please check the console for details.")
    } finally {
      setLoading(false)
    }
  }

  const addItem = () => {
    append({
      id: Date.now().toString(),
      description: "",
      quantity: 1,
      rate: 0,
      amount: 0,
    })
  }

  const removeItem = (index: number) => {
    if (fields.length > 1) {
      remove(index)
    }
  }

  return {
    form,
    fields,
    loading,
    isEditing,
    onSubmit,
    addItem,
    removeItem,
  }
}
