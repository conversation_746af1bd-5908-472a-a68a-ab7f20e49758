"use client"

import { Control } from "react-hook-form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { InvoiceFormData } from "@/lib/validations"
import { formatCurrency, type Currency } from "@/lib/utils/currency"

interface InvoiceTotalsSectionProps {
  control: Control<InvoiceFormData>
  subtotal: number
  totalAmount: number
  currency: Currency
  onTaxChange: (callback: () => void) => void
  onDiscountChange: (callback: () => void) => void
}

export function InvoiceTotalsSection({
  control,
  subtotal,
  totalAmount,
  currency,
  onTaxChange,
  onDiscountChange,
}: InvoiceTotalsSectionProps) {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <div className="flex justify-between">
          <span>Subtotal:</span>
          <span>{formatCurrency(subtotal || 0, currency)}</span>
        </div>

        {/* Discount Section */}
        <div className="space-y-2 border rounded-md p-3 bg-gray-50">
          <FormLabel className="text-sm font-medium">Discount</FormLabel>

          <div className="grid grid-cols-2 gap-2">
            <FormField
              control={control}
              name="discount_type"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder="Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="amount">Fixed Amount</SelectItem>
                        <SelectItem value="percentage">Percentage</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="discount_amount"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="0"
                      className="text-right"
                      {...field}
                      onChange={(e) => onDiscountChange(() => {
                        field.onChange(Number(e.target.value))
                      })}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <FormField
          control={control}
          name="tax_amount"
          render={({ field }) => (
            <FormItem>
              <div className="flex justify-between items-center">
                <FormLabel>Tax Amount:</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    className="w-24 text-right"
                    {...field}
                    onChange={(e) => onTaxChange(() => {
                      field.onChange(Number(e.target.value))
                    })}
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-between font-bold text-lg border-t pt-2">
          <span>Total:</span>
          <span>{formatCurrency(totalAmount || 0, currency)}</span>
        </div>
      </div>
    </div>
  )
}
