"use client"

import { useState, useEffect, useCallback } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { StatusBadge } from "@/components/ui/status-badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { getProjects, searchProjects, getProjectsByStatus, deleteProject } from "@/lib/api/projects-client"
import { ProjectWithRelations, ProjectStatus } from "@/lib/types"
import { Search, Plus, MoreHorizontal, Edit, Trash2, Eye, Calendar, Loader2 } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ProjectForm } from "./project-form"
import { formatDistanceToNow } from "date-fns"

interface ProjectListProps {
  onProjectSelect?: (project: ProjectWithRelations) => void
}

export function ProjectList({}: ProjectListProps) {
  const [projects, setProjects] = useState<ProjectWithRelations[]>([])
  const [loading, setLoading] = useState(true)
  const [searchInput, setSearchInput] = useState("")
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("")
  const [isSearching, setIsSearching] = useState(false)
  const [statusFilter, setStatusFilter] = useState<ProjectStatus | "all">("all")
  const [showForm, setShowForm] = useState(false)
  const [editingProject, setEditingProject] = useState<ProjectWithRelations | undefined>()

  // Debounce search input
  useEffect(() => {
    setIsSearching(true)
    const timeoutId = setTimeout(() => {
      // Only search if input has 3+ characters or is empty
      if (searchInput.length === 0 || searchInput.length >= 3) {
        setDebouncedSearchQuery(searchInput)
      }
      setIsSearching(false)
    }, 500) // 500ms debounce delay

    return () => {
      clearTimeout(timeoutId)
    }
  }, [searchInput])

  const fetchProjects = useCallback(async () => {
    setLoading(true)
    try {
      let data
      if (debouncedSearchQuery) {
        data = await searchProjects(debouncedSearchQuery)
      } else if (statusFilter !== "all") {
        data = await getProjectsByStatus(statusFilter)
      } else {
        data = await getProjects()
      }
      setProjects(data)
    } catch (error) {
      console.error("Failed to fetch projects:", error)
    } finally {
      setLoading(false)
    }
  }, [debouncedSearchQuery, statusFilter])

  useEffect(() => {
    fetchProjects()
  }, [fetchProjects])

  const handleDeleteProject = async (id: string) => {
    if (confirm("Are you sure you want to delete this project?")) {
      try {
        await deleteProject(id)
        fetchProjects()
      } catch (error) {
        console.error("Failed to delete project:", error)
      }
    }
  }

  const handleEditProject = (project: ProjectWithRelations) => {
    setEditingProject(project)
    setShowForm(true)
  }

  const handleFormSuccess = () => {
    fetchProjects()
    setEditingProject(undefined)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-2">
                <div className="h-4 bg-muted rounded w-1/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
                <div className="h-3 bg-muted rounded w-1/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Search and Filter Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          {isSearching && searchInput.length >= 3 && (
            <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4 animate-spin" />
          )}
          <Input
            placeholder="Search projects by name or description (min 3 characters)..."
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            className="pl-10 pr-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={(value: ProjectStatus | "all") => setStatusFilter(value)}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="planning">Planning</SelectItem>
            <SelectItem value="in_progress">In Progress</SelectItem>
            <SelectItem value="review">Review</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="cancelled">Cancelled</SelectItem>
          </SelectContent>
        </Select>
        <Button onClick={() => setShowForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Project
        </Button>
      </div>

      {/* Project List */}
      {loading ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </CardContent>
        </Card>
      ) : projects.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">
              {debouncedSearchQuery || statusFilter !== "all"
                ? "No projects found matching your criteria."
                : "No projects yet. Create your first project to get started."
              }
            </p>
            {searchInput.length > 0 && searchInput.length < 3 && (
              <p className="text-sm text-muted-foreground mt-2">
                Type at least 3 characters to search
              </p>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Client</TableHead>
                  <TableHead className="hidden sm:table-cell">Status</TableHead>
                  <TableHead className="hidden md:table-cell text-right">Budget</TableHead>
                  <TableHead className="hidden lg:table-cell">Due Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {projects.map((project) => (
                  <TableRow key={project.id}>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">{project.name}</span>
                        {project.description && (
                          <span className="text-sm text-muted-foreground line-clamp-1">
                            {project.description}
                          </span>
                        )}
                        {project.assigned_team_members && project.assigned_team_members.length > 0 && (
                          <div className="flex gap-1 flex-wrap mt-1">
                            {project.assigned_team_members.slice(0, 2).map((member) => (
                              <Badge key={member.id} variant="secondary" className="text-xs">
                                {member.full_name}
                              </Badge>
                            ))}
                            {project.assigned_team_members.length > 2 && (
                              <Badge variant="secondary" className="text-xs">
                                +{project.assigned_team_members.length - 2}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {project.client ? (
                        <div className="flex flex-col">
                          <span className="font-medium">{project.client.name}</span>
                          {project.client.company && (
                            <span className="text-sm text-muted-foreground">{project.client.company}</span>
                          )}
                        </div>
                      ) : (
                        <span className="text-sm text-muted-foreground">No client</span>
                      )}
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      <StatusBadge status={project.status} />
                    </TableCell>
                    <TableCell className="hidden md:table-cell text-right">
                      {project.budget ? (
                        <span className="font-medium">{formatCurrency(project.budget)}</span>
                      ) : (
                        <span className="text-sm text-muted-foreground">No budget</span>
                      )}
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      {project.end_date ? (
                        <div className="flex items-center gap-1 text-sm">
                          <Calendar className="h-3 w-3" />
                          {formatDistanceToNow(new Date(project.end_date), { addSuffix: true })}
                        </div>
                      ) : (
                        <span className="text-sm text-muted-foreground">No due date</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">More actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/projects/${project.id}`}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditProject(project)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeleteProject(project.id)}
                            className="text-destructive"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Project Form Dialog */}
      <ProjectForm
        open={showForm}
        onOpenChange={setShowForm}
        project={editingProject}
        onSuccess={handleFormSuccess}
      />
    </div>
  )
}
